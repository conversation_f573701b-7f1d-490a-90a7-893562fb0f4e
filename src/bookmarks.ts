/**
 * bookmarks.ts
 *
 * This script handles the bookmarks viewer page logic.
 * It retrieves saved bookmarks from chrome.storage.local and displays them
 * organized by groups with search and filter functionality.
 */

// Define the structure for our smart bookmark objects (matching background.ts)
interface SmartBookmark {
    url: string;
    title: string;
    description: string;
    lastAccessed: number;
    bookmarkedAt: number;
    visitCount: number;
    notes: string;
    favIconUrl?: string;
}

interface BookmarkSession {
    [category: string]: SmartBookmark[];
}

interface AllBookmarks {
    [sessionKey: string]: BookmarkSession;
}

// Global variables
let allBookmarks: AllBookmarks = {};
let filteredBookmarks: AllBookmarks = {};
let currentFilter = 'all';

// DOM elements
const loadingDiv = document.getElementById('loading') as HTMLDivElement;
const noBookmarksDiv = document.getElementById('no-bookmarks') as HTMLDivElement;
const bookmarksContainer = document.getElementById('bookmarks-container') as HTMLDivElement;
const searchInput = document.getElementById('search-input') as HTMLInputElement;
const filterButtons = document.querySelector('.filter-buttons') as HTMLDivElement;
const statsContainer = document.getElementById('stats-container') as HTMLDivElement;

// Initialize the bookmarks viewer
document.addEventListener('DOMContentLoaded', async () => {
    await loadBookmarks();
    setupEventListeners();
});

async function loadBookmarks() {
    try {
        // Get all stored data from chrome.storage.local
        const result = await chrome.storage.local.get(null);
        
        // Filter for bookmark sessions (keys starting with 'bookmarks_')
        const bookmarkSessions: AllBookmarks = {};
        for (const [key, value] of Object.entries(result)) {
            if (key.startsWith('bookmarks_') && typeof value === 'object') {
                bookmarkSessions[key] = value as BookmarkSession;
            }
        }

        allBookmarks = bookmarkSessions;
        filteredBookmarks = { ...allBookmarks };

        loadingDiv.style.display = 'none';

        if (Object.keys(allBookmarks).length === 0) {
            noBookmarksDiv.style.display = 'block';
            return;
        }

        renderStats();
        renderFilterButtons();
        renderBookmarks();

    } catch (error) {
        console.error('Error loading bookmarks:', error);
        loadingDiv.textContent = 'Error loading bookmarks';
    }
}

function renderStats() {
    let totalBookmarks = 0;
    let totalSessions = Object.keys(allBookmarks).length;
    const categories = new Set<string>();

    // Count bookmarks and collect categories
    for (const session of Object.values(allBookmarks)) {
        for (const [category, bookmarks] of Object.entries(session)) {
            categories.add(category);
            totalBookmarks += bookmarks.length;
        }
    }

    const statsHTML = `
        <div class="stat-card">
            <div class="stat-number">${totalBookmarks}</div>
            <div class="stat-label">Total Bookmarks</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${categories.size}</div>
            <div class="stat-label">Categories</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${totalSessions}</div>
            <div class="stat-label">Sessions</div>
        </div>
    `;

    statsContainer.innerHTML = statsHTML;
}

function renderFilterButtons() {
    const categories = new Set<string>();
    
    // Collect all unique categories
    for (const session of Object.values(allBookmarks)) {
        for (const category of Object.keys(session)) {
            categories.add(category);
        }
    }

    // Create filter buttons
    const sortedCategories = Array.from(categories).sort();
    const buttonsHTML = `
        <button class="filter-btn active" data-category="all">All</button>
        ${sortedCategories.map(category => 
            `<button class="filter-btn" data-category="${category}">${category}</button>`
        ).join('')}
    `;

    filterButtons.innerHTML = buttonsHTML;

    // Add event listeners to filter buttons
    filterButtons.addEventListener('click', (e) => {
        const target = e.target as HTMLButtonElement;
        if (target.classList.contains('filter-btn')) {
            // Update active button
            filterButtons.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            target.classList.add('active');
            
            // Update filter
            currentFilter = target.dataset.category || 'all';
            applyFilters();
        }
    });
}

function setupEventListeners() {
    // Search functionality
    searchInput.addEventListener('input', debounce(applyFilters, 300));
}

function applyFilters() {
    const searchTerm = searchInput.value.toLowerCase().trim();
    
    filteredBookmarks = {};

    for (const [sessionKey, session] of Object.entries(allBookmarks)) {
        const filteredSession: BookmarkSession = {};

        for (const [category, bookmarks] of Object.entries(session)) {
            // Apply category filter
            if (currentFilter !== 'all' && category !== currentFilter) {
                continue;
            }

            // Apply search filter
            const filteredBookmarksInCategory = bookmarks.filter(bookmark => {
                if (!searchTerm) return true;
                
                return (
                    bookmark.title.toLowerCase().includes(searchTerm) ||
                    bookmark.url.toLowerCase().includes(searchTerm) ||
                    bookmark.description.toLowerCase().includes(searchTerm)
                );
            });

            if (filteredBookmarksInCategory.length > 0) {
                filteredSession[category] = filteredBookmarksInCategory;
            }
        }

        if (Object.keys(filteredSession).length > 0) {
            filteredBookmarks[sessionKey] = filteredSession;
        }
    }

    renderBookmarks();
}

function renderBookmarks() {
    if (Object.keys(filteredBookmarks).length === 0) {
        bookmarksContainer.innerHTML = '<div class="no-bookmarks"><h2>No bookmarks match your criteria</h2><p>Try adjusting your search or filter settings.</p></div>';
        return;
    }

    let html = '';

    // Sort sessions by timestamp (newest first)
    const sortedSessions = Object.entries(filteredBookmarks).sort((a, b) => {
        const timestampA = parseInt(a[0].replace('bookmarks_', ''));
        const timestampB = parseInt(b[0].replace('bookmarks_', ''));
        return timestampB - timestampA;
    });

    for (const [sessionKey, session] of sortedSessions) {
        const sessionDate = new Date(parseInt(sessionKey.replace('bookmarks_', ''))).toLocaleDateString();
        
        // Sort categories alphabetically
        const sortedCategories = Object.entries(session).sort((a, b) => a[0].localeCompare(b[0]));

        for (const [category, bookmarks] of sortedCategories) {
            html += `
                <div class="bookmark-group">
                    <div class="group-header">
                        <div>
                            <div class="group-title">${category}</div>
                            <div style="font-size: 0.9rem; color: #666; margin-top: 0.25rem;">Session: ${sessionDate}</div>
                        </div>
                        <div class="group-count">${bookmarks.length}</div>
                    </div>
                    <div class="bookmark-list">
                        ${bookmarks.map(bookmark => renderBookmarkItem(bookmark)).join('')}
                    </div>
                </div>
            `;
        }
    }

    bookmarksContainer.innerHTML = html;
}

function renderBookmarkItem(bookmark: SmartBookmark): string {
    const favicon = bookmark.favIconUrl 
        ? `<img src="${bookmark.favIconUrl}" alt="" class="bookmark-favicon" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">`
        : '';
    
    const defaultFavicon = `<div class="bookmark-favicon default" ${bookmark.favIconUrl ? 'style="display:none;"' : ''}>${bookmark.title.charAt(0).toUpperCase()}</div>`;
    
    const lastAccessed = new Date(bookmark.lastAccessed).toLocaleDateString();
    const bookmarkedAt = new Date(bookmark.bookmarkedAt).toLocaleDateString();

    return `
        <div class="bookmark-item">
            ${favicon}
            ${defaultFavicon}
            <div class="bookmark-content">
                <a href="${bookmark.url}" class="bookmark-title" target="_blank" rel="noopener noreferrer">
                    ${escapeHtml(bookmark.title)}
                </a>
                <div class="bookmark-url">${escapeHtml(bookmark.url)}</div>
                ${bookmark.description ? `<div class="bookmark-description">${escapeHtml(bookmark.description)}</div>` : ''}
                <div class="bookmark-meta">
                    <span>Visits: ${bookmark.visitCount}</span>
                    <span>Last accessed: ${lastAccessed}</span>
                    <span>Bookmarked: ${bookmarkedAt}</span>
                </div>
            </div>
            <div class="bookmark-actions">
                <button class="action-btn" onclick="openInNewTab('${bookmark.url}')" title="Open in new tab">
                    🔗
                </button>
                <button class="action-btn" onclick="copyToClipboard('${bookmark.url}')" title="Copy URL">
                    📋
                </button>
            </div>
        </div>
    `;
}

// Utility functions
function escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function debounce(func: Function, wait: number) {
    let timeout: number;
    return function executedFunction(...args: any[]) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait) as any;
    };
}

// Global functions for button actions
(window as any).openInNewTab = function(url: string) {
    chrome.tabs.create({ url: url });
};

(window as any).copyToClipboard = function(url: string) {
    navigator.clipboard.writeText(url).then(() => {
        // Show a brief success message
        const button = event?.target as HTMLButtonElement;
        const originalText = button.textContent;
        button.textContent = '✓';
        setTimeout(() => {
            button.textContent = originalText;
        }, 1000);
    }).catch(err => {
        console.error('Failed to copy URL:', err);
    });
};
