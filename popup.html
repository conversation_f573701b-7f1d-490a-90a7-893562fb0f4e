<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BookSmart</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
            width: 280px;
            padding: 16px;
            background-color: #f9f9f9;
            text-align: center;
        }
        h1 {
            font-size: 18px;
            color: #333;
            margin-top: 0;
            margin-bottom: 16px;
        }
        button {
            width: 100%;
            padding: 12px;
            font-size: 16px;
            font-weight: 500;
            color: #fff;
            background: linear-gradient(45deg, #007BFF, #00BFFF);
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 8px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        button:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        #view-bookmarks-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        #status {
            margin-top: 16px;
            font-size: 14px;
            color: #555;
            min-height: 20px;
        }
    </style>
</head>
<body>
    <h1>BookSmart</h1>
    <button id="bookmark-all-btn">Bookmark All Tabs</button>
    <button id="view-bookmarks-btn">View Bookmarks</button>
    <div id="status"></div>
    <script src="js/popup.js"></script>
</body>
</html>
